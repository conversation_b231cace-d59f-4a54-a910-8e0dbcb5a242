msgid ""
msgstr ""
"Project-Id-Version: ThemeMakers Engorgio Features\n"
"POT-Creation-Date: 2016-02-29 21:37+0100\n"
"PO-Revision-Date: 2016-02-29 21:37+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.7\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: index.php\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;"
"_n_noop:1,2;_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: index.php:42
msgid "Tracking Code"
msgstr ""

#: index.php:45
msgid ""
"Place here your Google Analytics (or other) tracking code. It will be "
"inserted before closing head tag in your theme."
msgstr ""

#: index.php:69
msgid "Theme Slider"
msgstr ""

#: index.php:70
msgid "Group"
msgstr ""

#: index.php:71 index.php:113 index.php:132 index.php:173 index.php:212
#: index.php:252 index.php:276 index.php:316 index.php:339 index.php:362
#: index.php:384
msgid "Add New"
msgstr ""

#: index.php:72
msgid "Add New Slider Group"
msgstr ""

#: index.php:73
msgid "Edit Slider Group"
msgstr ""

#: index.php:74
msgid "New Slider Group"
msgstr ""

#: index.php:75
msgid "View Slider Group"
msgstr ""

#: index.php:76
msgid "Search Slider Groups"
msgstr ""

#: index.php:77
msgid "No Slide Groups found"
msgstr ""

#: index.php:78
msgid "No Slide Groups found in Trash"
msgstr ""

#: index.php:111 index.php:112 index.php:123
msgid "Position"
msgstr ""

#: index.php:114
msgid "Add New Position"
msgstr ""

#: index.php:115
msgid "Edit Position"
msgstr ""

#: index.php:116
msgid "New Position"
msgstr ""

#: index.php:117
msgid "View Position"
msgstr ""

#: index.php:118
msgid "Search GPosition"
msgstr ""

#: index.php:119
msgid "No Position found"
msgstr ""

#: index.php:120
msgid "No Position found in Trash"
msgstr ""

#: index.php:130 index.php:131
msgid "Staff"
msgstr ""

#: index.php:133
msgid "Add New Staff"
msgstr ""

#: index.php:134
msgid "Edit Staff"
msgstr ""

#: index.php:135
msgid "New Staff"
msgstr ""

#: index.php:136
msgid "View Staff"
msgstr ""

#: index.php:137
msgid "Search In Staff"
msgstr ""

#: index.php:138
msgid "Nothing found"
msgstr ""

#: index.php:139
msgid "Nothing found in Trash"
msgstr ""

#: index.php:171
msgid "Testimonials"
msgstr ""

#: index.php:172
msgid "Testimonial"
msgstr ""

#: index.php:174
msgid "Add New Testimonial"
msgstr ""

#: index.php:175
msgid "Edit Testimonial"
msgstr ""

#: index.php:176
msgid "New Testimonial"
msgstr ""

#: index.php:177
msgid "View Testimonial"
msgstr ""

#: index.php:178
msgid "Search Testimonials"
msgstr ""

#: index.php:179
msgid "No Testimonials found"
msgstr ""

#: index.php:180
msgid "No Testimonials found in Trash"
msgstr ""

#: index.php:210
msgid "Subscribers"
msgstr ""

#: index.php:211
msgid "Subscriber"
msgstr ""

#: index.php:213
msgid "Add New Subscriber"
msgstr ""

#: index.php:214
msgid "Edit Subscriber"
msgstr ""

#: index.php:215
msgid "New Subscriber"
msgstr ""

#: index.php:216
msgid "View Subscriber"
msgstr ""

#: index.php:217
msgid "Search Subscriber"
msgstr ""

#: index.php:218
msgid "No Subscribers found"
msgstr ""

#: index.php:219
msgid "No Subscribers found in Trash"
msgstr ""

#: index.php:250
msgid "Gallery Categories"
msgstr ""

#: index.php:251
msgid "Gallery Category"
msgstr ""

#: index.php:253
msgid "Add New Gallery Category"
msgstr ""

#: index.php:254
msgid "Edit Gallery Category"
msgstr ""

#: index.php:255
msgid "New Gallery Category"
msgstr ""

#: index.php:256
msgid "View Gallery Category"
msgstr ""

#: index.php:257
msgid "Search Gallery Categories"
msgstr ""

#: index.php:258
msgid "No Gallery Categories found"
msgstr ""

#: index.php:259
msgid "No Gallery Categories found in Trash"
msgstr ""

#: index.php:274
msgid "Galleries"
msgstr ""

#: index.php:275
msgid "Gallery"
msgstr ""

#: index.php:277
msgid "Add New Gallery"
msgstr ""

#: index.php:278
msgid "Edit Gallery"
msgstr ""

#: index.php:279
msgid "New Gallery"
msgstr ""

#: index.php:280
msgid "View Gallery"
msgstr ""

#: index.php:281
msgid "Search Gallery"
msgstr ""

#: index.php:282
msgid "No Galleries found"
msgstr ""

#: index.php:283
msgid "No Galleries found in Trash"
msgstr ""

#: index.php:314
msgid "Clients"
msgstr ""

#: index.php:315
msgid "Client"
msgstr ""

#: index.php:317
msgid "Add New Client"
msgstr ""

#: index.php:318
msgid "Edit Client"
msgstr ""

#: index.php:319
msgid "New Client"
msgstr ""

#: index.php:320
msgid "View Client"
msgstr ""

#: index.php:321
msgid "Search Clients"
msgstr ""

#: index.php:322
msgid "No Clients found"
msgstr ""

#: index.php:323
msgid "No Clients found in Trash"
msgstr ""

#: index.php:337
msgid "Skills"
msgstr ""

#: index.php:338
msgid "Skill"
msgstr ""

#: index.php:340
msgid "Add New Skill"
msgstr ""

#: index.php:341
msgid "Edit Skill"
msgstr ""

#: index.php:342
msgid "New Skill"
msgstr ""

#: index.php:343
msgid "View Skill"
msgstr ""

#: index.php:344
msgid "Search Skills"
msgstr ""

#: index.php:345
msgid "No Skills found"
msgstr ""

#: index.php:346
msgid "No Skills found in Trash"
msgstr ""

#: index.php:360
msgid "Categories"
msgstr ""

#: index.php:361
msgid "Category"
msgstr ""

#: index.php:363
msgid "Add New Category"
msgstr ""

#: index.php:364
msgid "Edit Category"
msgstr ""

#: index.php:365
msgid "New Category"
msgstr ""

#: index.php:366
msgid "View Category"
msgstr ""

#: index.php:367
msgid "Search Categories"
msgstr ""

#: index.php:368
msgid "No Categories found"
msgstr ""

#: index.php:369
msgid "No Categories found in Trash"
msgstr ""

#: index.php:382
msgid "Portfolios"
msgstr ""

#: index.php:383
msgid "Portfolio"
msgstr ""

#: index.php:385
msgid "Add New Portfolio"
msgstr ""

#: index.php:386
msgid "Edit Portfolio"
msgstr ""

#: index.php:387
msgid "New Portfolio"
msgstr ""

#: index.php:388
msgid "View Portfolio"
msgstr ""

#: index.php:389
msgid "Search Portfolios"
msgstr ""

#: index.php:390
msgid "No Portfolios found"
msgstr ""

#: index.php:391
msgid "No Portfolios found in Trash"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "ThemeMakers Engorgio Features"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "http://webtemplatemasters.com"
msgstr ""

#. Description of the plugin/theme
msgid "Advanced Features for Engorgio Theme"
msgstr ""

#. Author of the plugin/theme
msgid "ThemeMakers"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://themeforest.net/user/ThemeMakers"
msgstr ""
