/*
! tailwindcss v3.4.4 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */
  tab-size: 4; /* 3 */
  font-family: Figtree, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}

[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='checkbox']:checked {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
}

@media (forced-colors: active)  {

  [type='radio']:checked {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

@media (forced-colors: active)  {

  [type='checkbox']:indeterminate {
    -webkit-appearance: auto;
            appearance: auto;
  }
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}:root{--accent-st: 61 69 146;--accent-hover-st: 73 83 178;--accent-st-tertiary: 255 88 14;--zip-alert-error: 239 68 68;--zip-body-text: 71 85 105;--zip-app-highlight-bg: 246 250 254;--zip-app-heading: 15 23 42;--zip-dark-theme-heading: 252 252 253;--zip-dark-theme-content-background: 39 49 63;--zip-dark-theme-body: 228 234 241;--zip-dark-theme-border: 51 62 82;--zip-dark-theme-icon-active: 188 201 220;--zip-dark-theme-bg: 31 39 51;--zip-light-border-primary: 229 231 235;--zip-app-border-hover: 209 218 229;--zip-app-inactive-icon: 148 163 184;--zip-app-light-bg: 240 244 250;--zip-blue-crayola: 61 69 146}h1 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 2.25rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}h4 {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}h5 {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}h6 {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}p {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-body-text) / var(--tw-text-opacity));
}ul {
  list-style-image: none;
}li {
  margin: 0px;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-body-text) / var(--tw-text-opacity));
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}.container {
  width: 100%;
}@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}.pointer-events-none {
  pointer-events: none;
}.visible {
  visibility: visible;
}.invisible {
  visibility: hidden;
}.static {
  position: static;
}.fixed {
  position: fixed;
}.\!absolute {
  position: absolute !important;
}.absolute {
  position: absolute;
}.relative {
  position: relative;
}.sticky {
  position: sticky;
}.inset-0 {
  inset: 0px;
}.inset-2 {
  inset: 0.5rem;
}.inset-y-0 {
  top: 0px;
  bottom: 0px;
}.-top-1 {
  top: -0.25rem;
}.bottom-0 {
  bottom: 0px;
}.bottom-28 {
  bottom: 7rem;
}.bottom-5 {
  bottom: 1.25rem;
}.left-0 {
  left: 0px;
}.left-2 {
  left: 0.5rem;
}.left-2\.5 {
  left: 0.625rem;
}.left-4 {
  left: 1rem;
}.left-full {
  left: 100%;
}.right-0 {
  right: 0px;
}.right-10 {
  right: 2.5rem;
}.right-2 {
  right: 0.5rem;
}.right-20 {
  right: 5rem;
}.right-3 {
  right: 0.75rem;
}.right-4 {
  right: 1rem;
}.right-5 {
  right: 1.25rem;
}.top-0 {
  top: 0px;
}.top-1 {
  top: 0.25rem;
}.top-2 {
  top: 0.5rem;
}.top-3 {
  top: 0.75rem;
}.top-4 {
  top: 1rem;
}.top-5 {
  top: 1.25rem;
}.top-\[0\.875rem\] {
  top: 0.875rem;
}.top-\[45\%\] {
  top: 45%;
}.isolate {
  isolation: isolate;
}.z-0 {
  z-index: 0;
}.z-10 {
  z-index: 10;
}.z-20 {
  z-index: 20;
}.z-50 {
  z-index: 50;
}.z-\[1\] {
  z-index: 1;
}.z-\[2\] {
  z-index: 2;
}.z-\[5\] {
  z-index: 5;
}.z-\[99999999\] {
  z-index: 99999999;
}.z-\[999999\] {
  z-index: 999999;
}.z-auto {
  z-index: auto;
}.order-1 {
  order: 1;
}.order-2 {
  order: 2;
}.col-span-12 {
  grid-column: span 12 / span 12;
}.col-span-4 {
  grid-column: span 4 / span 4;
}.col-span-6 {
  grid-column: span 6 / span 6;
}.col-span-8 {
  grid-column: span 8 / span 8;
}.\!m-0 {
  margin: 0px !important;
}.m-0 {
  margin: 0px;
}.m-4 {
  margin: 1rem;
}.m-auto {
  margin: auto;
}.\!mx-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}.\!my-3 {
  margin-top: 0.75rem !important;
  margin-bottom: 0.75rem !important;
}.\!my-4 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}.mx-auto {
  margin-left: auto;
  margin-right: auto;
}.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}.my-32 {
  margin-top: 8rem;
  margin-bottom: 8rem;
}.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}.\!mb-1 {
  margin-bottom: 0.25rem !important;
}.\!mb-2 {
  margin-bottom: 0.5rem !important;
}.\!mb-4 {
  margin-bottom: 1rem !important;
}.\!mr-5 {
  margin-right: 1.25rem !important;
}.\!mt-1 {
  margin-top: 0.25rem !important;
}.\!mt-3 {
  margin-top: 0.75rem !important;
}.\!mt-5 {
  margin-top: 1.25rem !important;
}.\!mt-8 {
  margin-top: 2rem !important;
}.-ml-px {
  margin-left: -1px;
}.-mt-2 {
  margin-top: -0.5rem;
}.mb-0 {
  margin-bottom: 0px;
}.mb-0\.5 {
  margin-bottom: 0.125rem;
}.mb-10 {
  margin-bottom: 2.5rem;
}.mb-2 {
  margin-bottom: 0.5rem;
}.mb-4 {
  margin-bottom: 1rem;
}.mb-5 {
  margin-bottom: 1.25rem;
}.mb-6 {
  margin-bottom: 1.5rem;
}.mb-\[60px\] {
  margin-bottom: 60px;
}.mb-auto {
  margin-bottom: auto;
}.ml-0 {
  margin-left: 0px;
}.ml-1 {
  margin-left: 0.25rem;
}.ml-2 {
  margin-left: 0.5rem;
}.ml-3 {
  margin-left: 0.75rem;
}.ml-6 {
  margin-left: 1.5rem;
}.ml-auto {
  margin-left: auto;
}.mr-0 {
  margin-right: 0px;
}.mr-1 {
  margin-right: 0.25rem;
}.mr-1\.5 {
  margin-right: 0.375rem;
}.mr-2 {
  margin-right: 0.5rem;
}.mr-5 {
  margin-right: 1.25rem;
}.mr-auto {
  margin-right: auto;
}.mt-0 {
  margin-top: 0px;
}.mt-0\.5 {
  margin-top: 0.125rem;
}.mt-1 {
  margin-top: 0.25rem;
}.mt-1\.5 {
  margin-top: 0.375rem;
}.mt-2 {
  margin-top: 0.5rem;
}.mt-3 {
  margin-top: 0.75rem;
}.mt-4 {
  margin-top: 1rem;
}.mt-5 {
  margin-top: 1.25rem;
}.mt-6 {
  margin-top: 1.5rem;
}.mt-7 {
  margin-top: 1.75rem;
}.mt-8 {
  margin-top: 2rem;
}.mt-auto {
  margin-top: auto;
}.block {
  display: block;
}.inline-block {
  display: inline-block;
}.inline {
  display: inline;
}.flex {
  display: flex;
}.inline-flex {
  display: inline-flex;
}.table {
  display: table;
}.grid {
  display: grid;
}.hidden {
  display: none;
}.aspect-\[1\/1\] {
  aspect-ratio: 1/1;
}.aspect-\[1\/2\] {
  aspect-ratio: 1/2;
}.aspect-\[12\/8\] {
  aspect-ratio: 12/8;
}.aspect-\[164\/179\] {
  aspect-ratio: 164/179;
}.aspect-\[2\/1\] {
  aspect-ratio: 2/1;
}.aspect-\[2\/2\] {
  aspect-ratio: 2/2;
}.aspect-\[3\/3\] {
  aspect-ratio: 3/3;
}.aspect-\[3\/4\] {
  aspect-ratio: 3/4;
}.aspect-\[4\/3\] {
  aspect-ratio: 4/3;
}.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}.size-3\.5 {
  width: 0.875rem;
  height: 0.875rem;
}.size-4 {
  width: 1rem;
  height: 1rem;
}.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}.\!h-6 {
  height: 1.5rem !important;
}.\!h-\[calc\(100\%_-_44px\)\] {
  height: calc(100% - 44px) !important;
}.h-1 {
  height: 0.25rem;
}.h-10 {
  height: 2.5rem;
}.h-11 {
  height: 2.75rem;
}.h-12 {
  height: 3rem;
}.h-14 {
  height: 3.5rem;
}.h-16 {
  height: 4rem;
}.h-2 {
  height: 0.5rem;
}.h-2\.5 {
  height: 0.625rem;
}.h-20 {
  height: 5rem;
}.h-3 {
  height: 0.75rem;
}.h-32 {
  height: 8rem;
}.h-4 {
  height: 1rem;
}.h-4\.5 {
  height: 1.125rem;
}.h-40 {
  height: 10rem;
}.h-48 {
  height: 12rem;
}.h-5 {
  height: 1.25rem;
}.h-52 {
  height: 13rem;
}.h-6 {
  height: 1.5rem;
}.h-60 {
  height: 15rem;
}.h-7 {
  height: 1.75rem;
}.h-8 {
  height: 2rem;
}.h-9 {
  height: 2.25rem;
}.h-\[0\.875rem\] {
  height: 0.875rem;
}.h-\[14px\] {
  height: 14px;
}.h-\[2\.25rem\] {
  height: 2.25rem;
}.h-\[2\.625rem\] {
  height: 2.625rem;
}.h-\[20\.875rem\] {
  height: 20.875rem;
}.h-\[20px\] {
  height: 20px;
}.h-\[25px\] {
  height: 25px;
}.h-\[288px\] {
  height: 288px;
}.h-\[44px\] {
  height: 44px;
}.h-\[48px\] {
  height: 48px;
}.h-\[50px\] {
  height: 50px;
}.h-\[6px\] {
  height: 6px;
}.h-\[76px\] {
  height: 76px;
}.h-\[80px\] {
  height: 80px;
}.h-\[inherit\] {
  height: inherit;
}.h-auto {
  height: auto;
}.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}.h-full {
  height: 100%;
}.h-px {
  height: 1px;
}.h-screen {
  height: 100vh;
}.max-h-52 {
  max-height: 13rem;
}.max-h-60 {
  max-height: 15rem;
}.max-h-96 {
  max-height: 24rem;
}.max-h-\[180px\] {
  max-height: 180px;
}.max-h-\[19rem\] {
  max-height: 19rem;
}.max-h-\[250px\] {
  max-height: 250px;
}.max-h-full {
  max-height: 100%;
}.\!min-h-0 {
  min-height: 0px !important;
}.min-h-\[3\.125rem\] {
  min-height: 3.125rem;
}.min-h-\[36px\] {
  min-height: 36px;
}.min-h-\[45px\] {
  min-height: 45px;
}.min-h-\[48px\] {
  min-height: 48px;
}.min-h-\[50px\] {
  min-height: 50px;
}.min-h-full {
  min-height: 100%;
}.\!w-\[276px\] {
  width: 276px !important;
}.\!w-full {
  width: 100% !important;
}.w-1 {
  width: 0.25rem;
}.w-1\/2 {
  width: 50%;
}.w-1\/3 {
  width: 33.333333%;
}.w-10 {
  width: 2.5rem;
}.w-10\/12 {
  width: 83.333333%;
}.w-11 {
  width: 2.75rem;
}.w-11\/12 {
  width: 91.666667%;
}.w-16 {
  width: 4rem;
}.w-2 {
  width: 0.5rem;
}.w-2\.5 {
  width: 0.625rem;
}.w-20 {
  width: 5rem;
}.w-24 {
  width: 6rem;
}.w-3 {
  width: 0.75rem;
}.w-3\/4 {
  width: 75%;
}.w-32 {
  width: 8rem;
}.w-36 {
  width: 9rem;
}.w-4 {
  width: 1rem;
}.w-4\.5 {
  width: 1.125rem;
}.w-40 {
  width: 10rem;
}.w-48 {
  width: 12rem;
}.w-5 {
  width: 1.25rem;
}.w-52 {
  width: 13rem;
}.w-6 {
  width: 1.5rem;
}.w-60 {
  width: 15rem;
}.w-64 {
  width: 16rem;
}.w-7 {
  width: 1.75rem;
}.w-72 {
  width: 18rem;
}.w-8 {
  width: 2rem;
}.w-80 {
  width: 20rem;
}.w-9 {
  width: 2.25rem;
}.w-\[0\.875rem\] {
  width: 0.875rem;
}.w-\[100px\] {
  width: 100px;
}.w-\[106px\] {
  width: 106px;
}.w-\[11\.5rem\] {
  width: 11.5rem;
}.w-\[1440px\] {
  width: 1440px;
}.w-\[14px\] {
  width: 14px;
}.w-\[1700px\] {
  width: 1700px;
}.w-\[18\.25rem\] {
  width: 18.25rem;
}.w-\[18px\] {
  width: 18px;
}.w-\[1px\] {
  width: 1px;
}.w-\[2\.25rem\] {
  width: 2.25rem;
}.w-\[300px\] {
  width: 300px;
}.w-\[30px\] {
  width: 30px;
}.w-\[30rem\] {
  width: 30rem;
}.w-\[320px\] {
  width: 320px;
}.w-\[350px\] {
  width: 350px;
}.w-\[360px\] {
  width: 360px;
}.w-\[370px\] {
  width: 370px;
}.w-\[400px\] {
  width: 400px;
}.w-\[520px\] {
  width: 520px;
}.w-\[800px\] {
  width: 800px;
}.w-\[calc\(100\%_\+_2px\)\] {
  width: calc(100% + 2px);
}.w-auto {
  width: auto;
}.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}.w-full {
  width: 100%;
}.w-px {
  width: 1px;
}.w-screen {
  width: 100vw;
}.\!min-w-\[50px\] {
  min-width: 50px !important;
}.min-w-\[100px\] {
  min-width: 100px;
}.min-w-\[188px\] {
  min-width: 188px;
}.min-w-\[190px\] {
  min-width: 190px;
}.min-w-\[206px\] {
  min-width: 206px;
}.min-w-\[224px\] {
  min-width: 224px;
}.min-w-\[50px\] {
  min-width: 50px;
}.min-w-\[calc\(100\%_\/_2\)\] {
  min-width: calc(100% / 2);
}.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}.\!max-w-\[400px\] {
  max-width: 400px !important;
}.\!max-w-\[55rem\] {
  max-width: 55rem !important;
}.max-w-96 {
  max-width: 24rem;
}.max-w-\[32\.5rem\] {
  max-width: 32.5rem;
}.max-w-\[35rem\] {
  max-width: 35rem;
}.max-w-\[37\.5rem\] {
  max-width: 37.5rem;
}.max-w-\[500px\] {
  max-width: 500px;
}.max-w-\[590px\] {
  max-width: 590px;
}.max-w-container {
  max-width: 48rem;
}.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}.max-w-full {
  max-width: 100%;
}.max-w-max {
  max-width: max-content;
}.flex-1 {
  flex: 1 1 0%;
}.flex-auto {
  flex: 1 1 auto;
}.flex-shrink-0 {
  flex-shrink: 0;
}.\!shrink-0 {
  flex-shrink: 0 !important;
}.shrink-0 {
  flex-shrink: 0;
}.flex-grow {
  flex-grow: 1;
}.grow {
  flex-grow: 1;
}.origin-top-left {
  transform-origin: top left;
}.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-5 {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-\[0\.15rem\] {
  --tw-translate-x: 0.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-\[1\.15rem\] {
  --tw-translate-x: 1.15rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.scale-\[0\.33\] {
  --tw-scale-x: 0.33;
  --tw-scale-y: 0.33;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.scale-\[0\.8\] {
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}@keyframes pulse {

  50% {
    opacity: .5;
  }
}.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}.animate-rotate {
  animation: rotation 7s linear infinite;
}@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}.animate-spin {
  animation: spin 1s linear infinite;
}.cursor-default {
  cursor: default;
}.cursor-not-allowed {
  cursor: not-allowed;
}.cursor-pointer {
  cursor: pointer;
}.cursor-progress {
  cursor: progress;
}.select-none {
  -webkit-user-select: none;
          user-select: none;
}.resize {
  resize: both;
}.scroll-p-0 {
  scroll-padding: 0px;
}.list-decimal {
  list-style-type: decimal;
}.appearance-none {
  -webkit-appearance: none;
          appearance: none;
}.auto-rows-\[36px\] {
  grid-auto-rows: 36px;
}.auto-rows-auto {
  grid-auto-rows: auto;
}.auto-rows-min {
  grid-auto-rows: min-content;
}.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}.grid-cols-\[1fr_min-content\] {
  grid-template-columns: 1fr min-content;
}.grid-cols-\[8rem_1fr_8rem\] {
  grid-template-columns: 8rem 1fr 8rem;
}.grid-rows-1 {
  grid-template-rows: repeat(1, minmax(0, 1fr));
}.grid-rows-\[4rem_1fr\] {
  grid-template-rows: 4rem 1fr;
}.grid-rows-\[80px_1fr\] {
  grid-template-rows: 80px 1fr;
}.flex-row {
  flex-direction: row;
}.flex-col {
  flex-direction: column;
}.flex-col-reverse {
  flex-direction: column-reverse;
}.flex-wrap {
  flex-wrap: wrap;
}.flex-nowrap {
  flex-wrap: nowrap;
}.place-items-center {
  place-items: center;
}.content-stretch {
  align-content: stretch;
}.items-start {
  align-items: flex-start;
}.items-end {
  align-items: flex-end;
}.items-center {
  align-items: center;
}.justify-start {
  justify-content: flex-start;
}.justify-end {
  justify-content: flex-end;
}.justify-center {
  justify-content: center;
}.justify-between {
  justify-content: space-between;
}.justify-evenly {
  justify-content: space-evenly;
}.gap-0 {
  gap: 0px;
}.gap-0\.5 {
  gap: 0.125rem;
}.gap-1 {
  gap: 0.25rem;
}.gap-1\.5 {
  gap: 0.375rem;
}.gap-10 {
  gap: 2.5rem;
}.gap-2 {
  gap: 0.5rem;
}.gap-2\.5 {
  gap: 0.625rem;
}.gap-3 {
  gap: 0.75rem;
}.gap-4 {
  gap: 1rem;
}.gap-5 {
  gap: 1.25rem;
}.gap-6 {
  gap: 1.5rem;
}.gap-8 {
  gap: 2rem;
}.gap-\[26px\] {
  gap: 26px;
}.gap-x-2 {
  column-gap: 0.5rem;
}.gap-x-6 {
  column-gap: 1.5rem;
}.gap-x-8 {
  column-gap: 2rem;
}.gap-y-1 {
  row-gap: 0.25rem;
}.gap-y-4 {
  row-gap: 1rem;
}.gap-y-5 {
  row-gap: 1.25rem;
}.gap-y-6 {
  row-gap: 1.5rem;
}.\!space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)) !important;
}.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}.\!divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0 !important;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse))) !important;
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse)) !important;
}.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}.divide-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(0px * var(--tw-divide-x-reverse));
  border-left-width: calc(0px * calc(1 - var(--tw-divide-x-reverse)));
}.divide-solid > :not([hidden]) ~ :not([hidden]) {
  border-style: solid;
}.\!divide-border-primary > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1 !important;
  border-color: rgb(209 213 219 / var(--tw-divide-opacity)) !important;
}.divide-zip-dark-theme-border > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(var(--zip-dark-theme-border) / var(--tw-divide-opacity));
}.self-end {
  align-self: flex-end;
}.self-center {
  align-self: center;
}.overflow-auto {
  overflow: auto;
}.overflow-hidden {
  overflow: hidden;
}.overflow-clip {
  overflow: clip;
}.overflow-visible {
  overflow: visible;
}.overflow-x-auto {
  overflow-x: auto;
}.overflow-y-auto {
  overflow-y: auto;
}.overflow-x-hidden {
  overflow-x: hidden;
}.overflow-y-hidden {
  overflow-y: hidden;
}.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}.overflow-ellipsis {
  text-overflow: ellipsis;
}.whitespace-nowrap {
  white-space: nowrap;
}.whitespace-pre {
  white-space: pre;
}.whitespace-pre-wrap {
  white-space: pre-wrap;
}.text-nowrap {
  text-wrap: nowrap;
}.break-keep {
  word-break: keep-all;
}.\!rounded-none {
  border-radius: 0px !important;
}.rounded {
  border-radius: 0.25rem;
}.rounded-3xl {
  border-radius: 1.5rem;
}.rounded-\[25px\] {
  border-radius: 25px;
}.rounded-\[6px\] {
  border-radius: 6px;
}.rounded-\[inherit\] {
  border-radius: inherit;
}.rounded-full {
  border-radius: 9999px;
}.rounded-lg {
  border-radius: 0.5rem;
}.rounded-md {
  border-radius: 0.375rem;
}.rounded-none {
  border-radius: 0px;
}.rounded-sm {
  border-radius: 0.125rem;
}.rounded-xl {
  border-radius: 0.75rem;
}.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}.rounded-b-none {
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}.rounded-t-none {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}.rounded-bl-md {
  border-bottom-left-radius: 0.375rem;
}.rounded-br {
  border-bottom-right-radius: 0.25rem;
}.rounded-br-md {
  border-bottom-right-radius: 0.375rem;
}.rounded-tl-md {
  border-top-left-radius: 0.375rem;
}.rounded-tr {
  border-top-right-radius: 0.25rem;
}.rounded-tr-md {
  border-top-right-radius: 0.375rem;
}.\!border-0 {
  border-width: 0px !important;
}.border {
  border-width: 1px;
}.border-0 {
  border-width: 0px;
}.border-2 {
  border-width: 2px;
}.\!border-x-0 {
  border-left-width: 0px !important;
  border-right-width: 0px !important;
}.border-x {
  border-left-width: 1px;
  border-right-width: 1px;
}.border-x-0 {
  border-left-width: 0px;
  border-right-width: 0px;
}.border-y-0 {
  border-top-width: 0px;
  border-bottom-width: 0px;
}.\!border-t-0 {
  border-top-width: 0px !important;
}.border-b {
  border-bottom-width: 1px;
}.border-b-0 {
  border-bottom-width: 0px;
}.border-b-2 {
  border-bottom-width: 2px;
}.border-l {
  border-left-width: 1px;
}.border-l-0 {
  border-left-width: 0px;
}.border-r {
  border-right-width: 1px;
}.border-r-0 {
  border-right-width: 0px;
}.border-t {
  border-top-width: 1px;
}.border-t-0 {
  border-top-width: 0px;
}.border-solid {
  border-style: solid;
}.border-dashed {
  border-style: dashed;
}.border-none {
  border-style: none;
}.\!border-border-tertiary {
  --tw-border-opacity: 1 !important;
  border-color: rgb(216 223 233 / var(--tw-border-opacity)) !important;
}.\!border-transparent {
  border-color: transparent !important;
}.border-accent-hover-st\/80 {
  border-color: rgb(var(--accent-hover-st) / 0.8);
}.border-accent-st {
  --tw-border-opacity: 1;
  border-color: rgb(var(--accent-st) / var(--tw-border-opacity));
}.border-alert-error {
  --tw-border-opacity: 1;
  border-color: rgb(var(--zip-alert-error) / var(--tw-border-opacity));
}.border-alert-error-text {
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}.border-alert-error\/30 {
  border-color: rgb(var(--zip-alert-error) / 0.3);
}.border-alert-info-text {
  --tw-border-opacity: 1;
  border-color: rgb(61 69 146 / var(--tw-border-opacity));
}.border-blue-crayola {
  --tw-border-opacity: 1;
  border-color: rgb(var(--zip-blue-crayola) / var(--tw-border-opacity));
}.border-border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}.border-border-tertiary {
  --tw-border-opacity: 1;
  border-color: rgb(216 223 233 / var(--tw-border-opacity));
}.border-button-disabled {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}.border-credit-danger\/5 {
  border-color: rgb(234 21 34 / 0.05);
}.border-credit-warning\/5 {
  border-color: rgb(251 126 10 / 0.05);
}.border-ecommerce-border {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity));
}.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}.border-outline-color {
  --tw-border-opacity: 1;
  border-color: rgb(var( --accent-st-tertiary ) / var(--tw-border-opacity));
}.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity));
}.border-secondary-text {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}.border-step-connector {
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity));
}.border-transparent {
  border-color: transparent;
}.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}.border-zip-app-inactive-icon {
  --tw-border-opacity: 1;
  border-color: rgb(var(--zip-app-inactive-icon) / var(--tw-border-opacity));
}.border-zip-dark-theme-border {
  --tw-border-opacity: 1;
  border-color: rgb(var(--zip-dark-theme-border) / var(--tw-border-opacity));
}.border-zip-light-border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(var(--zip-light-border-primary) / var(--tw-border-opacity));
}.\!border-b-accent-st {
  --tw-border-opacity: 1 !important;
  border-bottom-color: rgb(var(--accent-st) / var(--tw-border-opacity)) !important;
}.border-opacity-\[0\.12\] {
  --tw-border-opacity: 0.12;
}.\!bg-transparent {
  background-color: transparent !important;
}.\!bg-zip-app-light-bg {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(var(--zip-app-light-bg) / var(--tw-bg-opacity)) !important;
}.bg-\[\#F6FAFE\] {
  --tw-bg-opacity: 1;
  background-color: rgb(246 250 254 / var(--tw-bg-opacity));
}.bg-\[\#dfdfe7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(223 223 231 / var(--tw-bg-opacity));
}.bg-accent-st {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--accent-st) / var(--tw-bg-opacity));
}.bg-accent-st-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}.bg-alert-error {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-alert-error) / var(--tw-bg-opacity));
}.bg-alert-error-bg {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}.bg-alert-error-text {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}.bg-alert-info-bg {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}.bg-background-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}.bg-background-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}.bg-background-tertiary {
  --tw-bg-opacity: 1;
  background-color: rgb(240 240 255 / var(--tw-bg-opacity));
}.bg-border-line-inactive {
  --tw-bg-opacity: 1;
  background-color: rgb(194 202 214 / var(--tw-bg-opacity));
}.bg-border-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}.bg-border-secondary\/80 {
  background-color: rgb(107 114 128 / 0.8);
}.bg-border-tertiary {
  --tw-bg-opacity: 1;
  background-color: rgb(216 223 233 / var(--tw-bg-opacity));
}.bg-button-disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}.bg-container-background {
  --tw-bg-opacity: 1;
  background-color: rgb(240 244 250 / var(--tw-bg-opacity));
}.bg-credit-danger {
  --tw-bg-opacity: 1;
  background-color: rgb(234 21 34 / var(--tw-bg-opacity));
}.bg-credit-danger\/5 {
  background-color: rgb(234 21 34 / 0.05);
}.bg-credit-warning {
  --tw-bg-opacity: 1;
  background-color: rgb(251 126 10 / var(--tw-bg-opacity));
}.bg-credit-warning\/5 {
  background-color: rgb(251 126 10 / 0.05);
}.bg-ecommerce-badge {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity));
}.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}.bg-inherit {
  background-color: inherit;
}.bg-nav-active {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}.bg-nav-inactive {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}.bg-neutral-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(212 212 212 / var(--tw-bg-opacity));
}.bg-outline-color {
  --tw-bg-opacity: 1;
  background-color: rgb(var( --accent-st-tertiary ) / var(--tw-bg-opacity));
}.bg-preview-background {
  --tw-bg-opacity: 1;
  background-color: rgb(242 244 247 / var(--tw-bg-opacity));
}.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}.bg-secondary-text {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}.bg-slate-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity));
}.bg-toast-error-icon {
  --tw-bg-opacity: 1;
  background-color: rgb(254 185 175 / var(--tw-bg-opacity));
}.bg-toast-info-icon {
  --tw-bg-opacity: 1;
  background-color: rgb(202 218 254 / var(--tw-bg-opacity));
}.bg-toast-success-icon {
  --tw-bg-opacity: 1;
  background-color: rgb(162 252 188 / var(--tw-bg-opacity));
}.bg-tooltip {
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity));
}.bg-transparent {
  background-color: transparent;
}.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}.bg-zip-app-heading {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-app-heading) / var(--tw-bg-opacity));
}.bg-zip-app-highlight-bg {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-app-highlight-bg) / var(--tw-bg-opacity));
}.bg-zip-app-light-bg {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-app-light-bg) / var(--tw-bg-opacity));
}.bg-zip-dark-theme-bg {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-dark-theme-bg) / var(--tw-bg-opacity));
}.bg-zip-dark-theme-border {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-dark-theme-border) / var(--tw-bg-opacity));
}.bg-zip-dark-theme-content-background {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-dark-theme-content-background) / var(--tw-bg-opacity));
}.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}.bg-gradient-1 {
  background-image: linear-gradient(90deg, #B809A7 0%, #E90B76 46.88%, #FC8536 100%);
}.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}.from-gradient-color-1 {
  --tw-gradient-from: #B809A7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(184 9 167 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-gradient-color-1\/50 {
  --tw-gradient-from: rgb(184 9 167 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(184 9 167 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.from-70\% {
  --tw-gradient-from-position: 70%;
}.via-gradient-color-2 {
  --tw-gradient-to: rgb(233 11 118 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #E90B76 var(--tw-gradient-via-position), var(--tw-gradient-to);
}.via-gradient-color-2\/50 {
  --tw-gradient-to: rgb(233 11 118 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(233 11 118 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}.via-46\.88 {
  --tw-gradient-via-position: 46.88%;
}.to-gradient-color-3 {
  --tw-gradient-to: #FC8536 var(--tw-gradient-to-position);
}.to-gradient-color-3\/50 {
  --tw-gradient-to: rgb(252 133 54 / 0.5) var(--tw-gradient-to-position);
}.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}.bg-\[length\:100\%_6px\] {
  background-size: 100% 6px;
}.bg-bottom {
  background-position: bottom;
}.bg-no-repeat {
  background-repeat: no-repeat;
}.stroke-2 {
  stroke-width: 2;
}.object-contain {
  object-fit: contain;
}.\!p-0 {
  padding: 0px !important;
}.\!p-6 {
  padding: 1.5rem !important;
}.p-0 {
  padding: 0px;
}.p-0\.5 {
  padding: 0.125rem;
}.p-1 {
  padding: 0.25rem;
}.p-12 {
  padding: 3rem;
}.p-2 {
  padding: 0.5rem;
}.p-2\.5 {
  padding: 0.625rem;
}.p-3 {
  padding: 0.75rem;
}.p-4 {
  padding: 1rem;
}.p-5 {
  padding: 1.25rem;
}.p-7 {
  padding: 1.75rem;
}.p-8 {
  padding: 2rem;
}.p-\[0\.1875rem\] {
  padding: 0.1875rem;
}.p-\[2px\] {
  padding: 2px;
}.p-px {
  padding: 1px;
}.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}.px-\[1\.875rem\] {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}.px-\[1rem\] {
  padding-left: 1rem;
  padding-right: 1rem;
}.px-\[20px\] {
  padding-left: 20px;
  padding-right: 20px;
}.px-\[60px\] {
  padding-left: 60px;
  padding-right: 60px;
}.px-\[78px\] {
  padding-left: 78px;
  padding-right: 78px;
}.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}.py-4\.75 {
  padding-top: 1.1875rem;
  padding-bottom: 1.1875rem;
}.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}.py-\[1\.625rem\] {
  padding-top: 1.625rem;
  padding-bottom: 1.625rem;
}.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}.py-\[28px\] {
  padding-top: 28px;
  padding-bottom: 28px;
}.py-\[3\.125rem\] {
  padding-top: 3.125rem;
  padding-bottom: 3.125rem;
}.py-\[3px\] {
  padding-top: 3px;
  padding-bottom: 3px;
}.py-\[50px\] {
  padding-top: 50px;
  padding-bottom: 50px;
}.py-\[93px\] {
  padding-top: 93px;
  padding-bottom: 93px;
}.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}.\!pl-10 {
  padding-left: 2.5rem !important;
}.\!pl-11 {
  padding-left: 2.75rem !important;
}.\!pl-\[18px\] {
  padding-left: 18px !important;
}.\!pr-10 {
  padding-right: 2.5rem !important;
}.\!pr-\[18px\] {
  padding-right: 18px !important;
}.pb-0 {
  padding-bottom: 0px;
}.pb-10 {
  padding-bottom: 2.5rem;
}.pb-14 {
  padding-bottom: 3.5rem;
}.pb-3 {
  padding-bottom: 0.75rem;
}.pb-4 {
  padding-bottom: 1rem;
}.pb-5 {
  padding-bottom: 1.25rem;
}.pb-6 {
  padding-bottom: 1.5rem;
}.pb-8 {
  padding-bottom: 2rem;
}.pl-2 {
  padding-left: 0.5rem;
}.pl-2\.5 {
  padding-left: 0.625rem;
}.pl-3 {
  padding-left: 0.75rem;
}.pl-4 {
  padding-left: 1rem;
}.pl-5 {
  padding-left: 1.25rem;
}.pl-6 {
  padding-left: 1.5rem;
}.pl-\[23px\] {
  padding-left: 23px;
}.pr-1 {
  padding-right: 0.25rem;
}.pr-1\.5 {
  padding-right: 0.375rem;
}.pr-10 {
  padding-right: 2.5rem;
}.pr-11 {
  padding-right: 2.75rem;
}.pr-12 {
  padding-right: 3rem;
}.pr-2 {
  padding-right: 0.5rem;
}.pr-3 {
  padding-right: 0.75rem;
}.pr-4 {
  padding-right: 1rem;
}.pr-5 {
  padding-right: 1.25rem;
}.pr-6 {
  padding-right: 1.5rem;
}.pr-8 {
  padding-right: 2rem;
}.pr-9 {
  padding-right: 2.25rem;
}.pr-\[25px\] {
  padding-right: 25px;
}.pt-0 {
  padding-top: 0px;
}.pt-0\.5 {
  padding-top: 0.125rem;
}.pt-1 {
  padding-top: 0.25rem;
}.pt-10 {
  padding-top: 2.5rem;
}.pt-2 {
  padding-top: 0.5rem;
}.pt-3 {
  padding-top: 0.75rem;
}.pt-4 {
  padding-top: 1rem;
}.pt-5 {
  padding-top: 1.25rem;
}.pt-6 {
  padding-top: 1.5rem;
}.pt-8 {
  padding-top: 2rem;
}.text-left {
  text-align: left;
}.text-center {
  text-align: center;
}.text-right {
  text-align: right;
}.text-justify {
  text-align: justify;
}.text-start {
  text-align: start;
}.align-baseline {
  vertical-align: baseline;
}.font-sans {
  font-family: Figtree, sans-serif;
}.\!text-\[1\.375rem\] {
  font-size: 1.375rem !important;
}.\!text-\[14px\] {
  font-size: 14px !important;
}.\!text-base {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}.\!text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}.\!text-xl {
  font-size: 1.25rem !important;
  line-height: 1.75rem !important;
}.\!text-xs {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
}.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.text-\[0\.625rem\] {
  font-size: 0.625rem;
}.text-\[0\.9rem\] {
  font-size: 0.9rem;
}.text-\[1\.75rem\] {
  font-size: 1.75rem;
}.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.\!font-normal {
  font-weight: 400 !important;
}.\!font-semibold {
  font-weight: 600 !important;
}.font-bold {
  font-weight: 700;
}.font-medium {
  font-weight: 500;
}.font-normal {
  font-weight: 400;
}.font-semibold {
  font-weight: 600;
}.uppercase {
  text-transform: uppercase;
}.capitalize {
  text-transform: capitalize;
}.\!leading-4 {
  line-height: 1rem !important;
}.\!leading-5 {
  line-height: 1.25rem !important;
}.\!leading-6 {
  line-height: 1.5rem !important;
}.leading-3 {
  line-height: .75rem;
}.leading-4 {
  line-height: 1rem;
}.leading-5 {
  line-height: 1.25rem;
}.leading-6 {
  line-height: 1.5rem;
}.leading-8 {
  line-height: 2rem;
}.leading-9 {
  line-height: 2.25rem;
}.leading-\[1\.375rem\] {
  line-height: 1.375rem;
}.leading-\[14px\] {
  line-height: 14px;
}.leading-\[150\%\] {
  line-height: 150%;
}.leading-\[1em\] {
  line-height: 1em;
}.leading-\[21px\] {
  line-height: 21px;
}.tracking-normal {
  letter-spacing: 0em;
}.\!text-alert-error {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-alert-error) / var(--tw-text-opacity)) !important;
}.\!text-nav-active {
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}.\!text-secondary-text {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}.\!text-zip-app-heading {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity)) !important;
}.\!text-zip-app-inactive-icon {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-app-inactive-icon) / var(--tw-text-opacity)) !important;
}.\!text-zip-body-text {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-body-text) / var(--tw-text-opacity)) !important;
}.\!text-zip-dark-theme-body {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-dark-theme-body) / var(--tw-text-opacity)) !important;
}.\!text-zip-dark-theme-heading {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-dark-theme-heading) / var(--tw-text-opacity)) !important;
}.text-\[\#0F172A\] {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}.text-accent-st {
  --tw-text-opacity: 1;
  color: rgb(var(--accent-st) / var(--tw-text-opacity));
}.text-alert-error {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-alert-error) / var(--tw-text-opacity));
}.text-alert-info {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}.text-alert-success {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}.text-background-primary {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}.text-blue-crayola {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-blue-crayola) / var(--tw-text-opacity));
}.text-body-text {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}.text-border-secondary {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}.text-credit-danger {
  --tw-text-opacity: 1;
  color: rgb(234 21 34 / var(--tw-text-opacity));
}.text-credit-warning {
  --tw-text-opacity: 1;
  color: rgb(251 126 10 / var(--tw-text-opacity));
}.text-ecommerce-text {
  --tw-text-opacity: 1;
  color: rgb(6 78 59 / var(--tw-text-opacity));
}.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}.text-heading-text {
  --tw-text-opacity: 1;
  color: rgb(3 7 18 / var(--tw-text-opacity));
}.text-icon-tertiary {
  --tw-text-opacity: 1;
  color: rgb(188 201 220 / var(--tw-text-opacity));
}.text-nav-active {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}.text-nav-inactive {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}.text-secondary-text {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}.text-st-background-secondary {
  --tw-text-opacity: 1;
  color: rgb(247 247 249 / var(--tw-text-opacity));
}.text-support-success-inverse {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}.text-zip-app-heading {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}.text-zip-app-inactive-icon {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-inactive-icon) / var(--tw-text-opacity));
}.text-zip-body-text {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-body-text) / var(--tw-text-opacity));
}.text-zip-dark-theme-body {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-dark-theme-body) / var(--tw-text-opacity));
}.text-zip-dark-theme-content-background {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-dark-theme-content-background) / var(--tw-text-opacity));
}.text-zip-dark-theme-heading {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-dark-theme-heading) / var(--tw-text-opacity));
}.text-zip-dark-theme-icon-active {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-dark-theme-icon-active) / var(--tw-text-opacity));
}.underline {
  text-decoration-line: underline;
}.no-underline {
  text-decoration-line: none;
}.\!opacity-80 {
  opacity: 0.8 !important;
}.opacity-0 {
  opacity: 0;
}.opacity-100 {
  opacity: 1;
}.opacity-25 {
  opacity: 0.25;
}.opacity-70 {
  opacity: 0.7;
}.opacity-75 {
  opacity: 0.75;
}.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-\[1px_2px_5px_1px_rgba\(0\2c 0\2c 0\2c 0\.15\)\] {
  --tw-shadow: 1px 2px 5px 1px rgba(0,0,0,0.15);
  --tw-shadow-colored: 1px 2px 5px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-error {
  --tw-shadow: 0px 1px 1px 0px #EF4444, 0px 0px 0px 1px #EF4444;
  --tw-shadow-colored: 0px 1px 1px 0px var(--tw-shadow-color), 0px 0px 0px 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-medium {
  --tw-shadow: 0px 4px 6px -2px rgba(0, 0, 0, 0.03), 0px 12px 16px -4px rgba(0, 0, 0, 0.08);
  --tw-shadow-colored: 0px 4px 6px -2px var(--tw-shadow-color), 0px 12px 16px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-sm {
  --tw-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-small {
  --tw-shadow: 0px 2px 4px -2px rgba(0, 0, 0, 0.06), 0px 4px 8px -2px rgba(0, 0, 0, 0.10);
  --tw-shadow-colored: 0px 2px 4px -2px var(--tw-shadow-color), 0px 4px 8px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-template-info {
  --tw-shadow: 0px -20px 25px -5px rgba(0, 0, 0, 0.10);
  --tw-shadow-colored: 0px -20px 25px -5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-template-preview {
  --tw-shadow: 0px 40px 120px -16px rgba(0, 0, 0, 0.30);
  --tw-shadow-colored: 0px 40px 120px -16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.outline {
  outline-style: solid;
}.outline-1 {
  outline-width: 1px;
}.outline-offset-2 {
  outline-offset: 2px;
}.outline-outline-color {
  outline-color: rgb(var( --accent-st-tertiary ) / 1);
}.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.ring-inset {
  --tw-ring-inset: inset;
}.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}.ring-transparent {
  --tw-ring-color: transparent;
}.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.duration-100 {
  transition-duration: 100ms;
}.duration-150 {
  transition-duration: 150ms;
}.duration-200 {
  transition-duration: 200ms;
}.duration-300 {
  transition-duration: 300ms;
}.duration-75 {
  transition-duration: 75ms;
}.duration-\[250ms\] {
  transition-duration: 250ms;
}.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}.\[grid-area\:1\/3\] {
  grid-area: 1/3;
}.zw-xxs-medium{font-size:.625rem;font-weight:500;line-height:.75rem}.zw-xs-normal{font-size:.75rem;font-weight:400;line-height:1rem}.zw-xs-medium{font-size:.75rem;font-weight:500;line-height:1rem}.zw-xs-semibold{font-size:.75rem;font-weight:600;line-height:1rem}.zw-sm-normal{font-size:.875rem;font-weight:400;line-height:1.25rem}.zw-sm-medium{font-size:.875rem;font-weight:500;line-height:1.25rem}.zw-sm-semibold{font-size:.875rem;font-weight:600;line-height:1.25rem}.zw-base-normal{font-size:1rem;font-weight:400;line-height:1.5rem}.zw-base-medium{font-size:1rem;font-weight:500;line-height:1.5rem}.zw-base-semibold{font-size:1rem;font-weight:600;line-height:1.5rem}.zw-base-bold{font-size:1rem;font-weight:700;line-height:1.5rem}.zw-h1{font-size:1.875rem;font-weight:700;line-height:2.25rem;color:var(--app-heading)}.zw-h2{font-size:1.5rem;font-weight:600;line-height:2rem;color:var(--app-heading)}.zw-h3{font-size:1.25rem;font-weight:600;line-height:1.25rem;color:var(--app-heading)}.zw-h4{font-size:1.125rem;font-weight:700;line-height:1.75rem;color:var(--app-heading)}.zw-h5{font-size:1rem;font-weight:600;line-height:1rem;color:var(--app-heading)}.zw-h6{font-size:.875rem;font-weight:600;line-height:.875rem;color:var(--app-heading)}.zw-tooltip{background:#334155;color:#fff;border-radius:4px}.zw-tooltip>.tippy-content{padding:2px 4px;font-size:.75rem;font-weight:400;line-height:1rem}.zw-tooltip__classic{background:#334155;color:#fff}.zw-tooltip__classic>.tippy-content{padding:5px 9px}.zw-tooltip__material {
  display: block !important;
  border-radius: 0.375rem !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}.zw-tooltip__material p {
  margin: 0px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(var(--zip-body-text) / var(--tw-text-opacity));
}html.wp-toolbar{padding:0}.ai-builder #adminmenumain,.ai-builder #wpadminbar,.ai-builder #adminmenuback,.ai-builder #adminmenuwrap,.ai-builder #wpfooter{display:none}.ai-builder #wpcontent,.ai-builder.auto-fold #wpcontent{margin:0;padding:0}.ai-builder.appearance_page_ai-builder #wpbody-content,.ai-builder #wpbody{padding:0}body{overflow-y:hidden}body * {
  box-sizing: border-box;
  font-family: Figtree, sans-serif;
}::-webkit-scrollbar{width:8px}::-webkit-scrollbar-track{border-radius:0}::-webkit-scrollbar-thumb{background:#d5d6d7;border-radius:0}::-webkit-scrollbar-thumb:hover{background:#d1d2d3;cursor:pointer}.st-link,a{cursor:pointer;text-decoration:underline;color:var(--st-color-accent)}.st-link:hover,a:hover{color:var(--st-color-accent-hover)}@keyframes rotate-alternate{from{transform:rotate(360deg)}to{transform:rotate(0deg)}}@keyframes ist-fadeinUp{0%{opacity:0;transform:translateY(10px)}100%{opacity:1;transform:translateY(0)}}.step-content.customize-business-logo .content-wrapper{animation:logo-screen-fadeIn 500ms}@keyframes logo-screen-fadeIn{0%{opacity:.3}100%{opacity:1}}.step-content.customize-typography-colors .content-wrapper{animation:colors-screen-fadeIn 500ms}@keyframes colors-screen-fadeIn{0%{opacity:.3}100%{opacity:1}}.hide-scrollbar{scrollbar-width:none;-ms-overflow-style:none}.hide-scrollbar::-webkit-scrollbar{display:none}.gradient-border-bottom{text-align:center;border-bottom:5px solid rgba(0,0,0,0);border-image:linear-gradient(90deg, #5a03ef 0% #fe5be4 100%);border-image-slice:1;width:100%;opacity:.5}.gradient-border-cover{position:relative}.gradient-border-cover::before{content:"";position:absolute;inset:0;border-radius:50px;padding:2px;background:linear-gradient(180deg, #5a03ef 0%, #fe5be4 100%);mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;pointer-events:none}.gradient-border-cover-button::before{border-radius:12px !important;padding:1px !important;inset:-1px}#spectra-onboarding-ai :disabled,#spectra-onboarding-ai [data-disabled=true] {
  pointer-events: none;
  opacity: 0.7;
}#spectra-onboarding-ai div:has(>:disabled),#spectra-onboarding-ai div:has(>[data-disabled=true]) {
  cursor: not-allowed;
}.scrollbar-hide::-webkit-scrollbar{display:none}.scrollbar-hide{-ms-overflow-style:none;scrollbar-width:none}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow::before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow::before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow::before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow::before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box .tippy-arrow {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity));width:16px;height:16px;
}.tippy-box .tippy-arrow::before{content:"";position:absolute;border-color:rgba(0,0,0,0);border-style:solid}.failed-site-container::-webkit-scrollbar-thumb{border-radius:20px}.placeholder\:\!text-\[0\.9rem\]::placeholder {
  font-size: 0.9rem !important;
}.placeholder\:\!text-base::placeholder {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
}.placeholder\:\!text-zip-app-inactive-icon::placeholder {
  --tw-text-opacity: 1 !important;
  color: rgb(var(--zip-app-inactive-icon) / var(--tw-text-opacity)) !important;
}.placeholder\:text-secondary-text::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}.before\:visible::before {
  content: var(--tw-content);
  visibility: visible;
}.before\:invisible::before {
  content: var(--tw-content);
  visibility: hidden;
}.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}.before\:relative::before {
  content: var(--tw-content);
  position: relative;
}.before\:-top-\[5px\]::before {
  content: var(--tw-content);
  top: -5px;
}.before\:mx-2::before {
  content: var(--tw-content);
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}.before\:mx-2\.5::before {
  content: var(--tw-content);
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}.before\:block::before {
  content: var(--tw-content);
  display: block;
}.before\:inline-block::before {
  content: var(--tw-content);
  display: inline-block;
}.before\:h-0::before {
  content: var(--tw-content);
  height: 0px;
}.before\:h-2::before {
  content: var(--tw-content);
  height: 0.5rem;
}.before\:h-px::before {
  content: var(--tw-content);
  height: 1px;
}.before\:w-2::before {
  content: var(--tw-content);
  width: 0.5rem;
}.before\:w-5::before {
  content: var(--tw-content);
  width: 1.25rem;
}.before\:\!rotate-45::before {
  content: var(--tw-content);
  --tw-rotate: 45deg !important;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
}.before\:border-l::before {
  content: var(--tw-content);
  border-left-width: 1px;
}.before\:border-t::before {
  content: var(--tw-content);
  border-top-width: 1px;
}.before\:border-border-primary::before {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}.before\:bg-inherit::before {
  content: var(--tw-content);
  background-color: inherit;
}.before\:text-sm::before {
  content: var(--tw-content);
  font-size: 0.875rem;
  line-height: 1.25rem;
}.before\:font-bold::before {
  content: var(--tw-content);
  font-weight: 700;
}.before\:content-\[\'\'\]::before {
  --tw-content: '';
  content: var(--tw-content);
}.before\:content-\[attr\(data-title\)\]::before {
  --tw-content: attr(data-title);
  content: var(--tw-content);
}.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}.after\:relative::after {
  content: var(--tw-content);
  position: relative;
}.after\:inset-0::after {
  content: var(--tw-content);
  inset: 0px;
}.after\:-top-\[5px\]::after {
  content: var(--tw-content);
  top: -5px;
}.after\:z-\[-1\]::after {
  content: var(--tw-content);
  z-index: -1;
}.after\:mx-2::after {
  content: var(--tw-content);
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}.after\:mx-2\.5::after {
  content: var(--tw-content);
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}.after\:inline-block::after {
  content: var(--tw-content);
  display: inline-block;
}.after\:h-px::after {
  content: var(--tw-content);
  height: 1px;
}.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}.after\:-translate-y-1::after {
  content: var(--tw-content);
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.after\:scale-75::after {
  content: var(--tw-content);
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.after\:bg-gradient-to-r::after {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}.after\:from-\[\#0A21F8\]::after {
  content: var(--tw-content);
  --tw-gradient-from: #0A21F8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(10 33 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.after\:via-\[\#9933FF\]::after {
  content: var(--tw-content);
  --tw-gradient-to: rgb(153 51 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #9933FF var(--tw-gradient-via-position), var(--tw-gradient-to);
}.after\:to-\[\#FC65D2\]::after {
  content: var(--tw-content);
  --tw-gradient-to: #FC65D2 var(--tw-gradient-to-position);
}.after\:opacity-50::after {
  content: var(--tw-content);
  opacity: 0.5;
}.after\:blur-xl::after {
  content: var(--tw-content);
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}.only\:\!p-0:only-child {
  padding: 0px !important;
}.only\:\!py-0:only-child {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}.focus-within\:\!border-0:focus-within {
  border-width: 0px !important;
}.focus-within\:border-accent-st:focus-within {
  --tw-border-opacity: 1;
  border-color: rgb(var(--accent-st) / var(--tw-border-opacity));
}.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus-within\:ring-1:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus-within\:ring-accent-st:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--accent-st) / var(--tw-ring-opacity));
}.hover\:\!bg-zip-app-light-bg:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(var(--zip-app-light-bg) / var(--tw-bg-opacity)) !important;
}.hover\:bg-\[\#F9FAFB\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}.hover\:bg-accent-hover-st:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--accent-hover-st) / var(--tw-bg-opacity));
}.hover\:bg-background-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}.hover\:bg-container-background:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 244 250 / var(--tw-bg-opacity));
}.hover\:bg-zip-dark-theme-content-background:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-dark-theme-content-background) / var(--tw-bg-opacity));
}.hover\:text-icon-secondary:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}.hover\:text-nav-active:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}.hover\:text-zip-app-heading:hover {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-heading) / var(--tw-text-opacity));
}.hover\:text-zip-app-inactive-icon:hover {
  --tw-text-opacity: 1;
  color: rgb(var(--zip-app-inactive-icon) / var(--tw-text-opacity));
}.hover\:underline:hover {
  text-decoration-line: underline;
}.focus\:\!border-0:focus {
  border-width: 0px !important;
}.focus\:\!border-accent-st:focus {
  --tw-border-opacity: 1 !important;
  border-color: rgb(var(--accent-st) / var(--tw-border-opacity)) !important;
}.focus\:border-accent-st:focus {
  --tw-border-opacity: 1;
  border-color: rgb(var(--accent-st) / var(--tw-border-opacity));
}.focus\:bg-zip-app-light-bg:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(var(--zip-app-light-bg) / var(--tw-bg-opacity));
}.focus\:\!shadow-none:focus {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}.focus\:shadow-none:focus {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.focus\:\!outline-none:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus\:\!ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}.focus\:\!ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-accent-st:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--accent-st) / var(--tw-ring-opacity));
}.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus-visible\:outline:focus-visible {
  outline-style: solid;
}.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}.focus-visible\:ring-accent-st:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(var(--accent-st) / var(--tw-ring-opacity));
}.active\:border-accent-st:active {
  --tw-border-opacity: 1;
  border-color: rgb(var(--accent-st) / var(--tw-border-opacity));
}.active\:outline-none:active {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}.group:active .group-active\:text-nav-active {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}.peer:focus ~ .peer-focus\:text-nav-inactive {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}@media not all and (min-width: 768px) {

  .max-md\:hidden {
    display: none;
  }

  .max-md\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}@media (min-width: 640px) {

  .sm\:right-0 {
    right: 0px;
  }

  .sm\:top-2 {
    top: 0.5rem;
  }

  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .sm\:h-\[268px\] {
    height: 268px;
  }

  .sm\:w-32 {
    width: 8rem;
  }

  .sm\:w-64 {
    width: 16rem;
  }

  .sm\:w-\[27\.5rem\] {
    width: 27.5rem;
  }

  .sm\:w-\[443px\] {
    width: 443px;
  }

  .sm\:w-\[480px\] {
    width: 480px;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:max-w-2xl {
    max-width: 42rem;
  }

  .sm\:max-w-\[24rem\] {
    max-width: 24rem;
  }

  .sm\:max-w-\[29rem\] {
    max-width: 29rem;
  }

  .sm\:max-w-\[30rem\] {
    max-width: 30rem;
  }

  .sm\:max-w-\[32\.5rem\] {
    max-width: 32.5rem;
  }

  .sm\:max-w-\[35rem\] {
    max-width: 35rem;
  }

  .sm\:max-w-\[40rem\] {
    max-width: 40rem;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:p-0 {
    padding: 0px;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:leading-6 {
    line-height: 1.5rem;
  }

  .sm\:leading-7 {
    line-height: 1.75rem;
  }

  .sm\:before\:w-12::before {
    content: var(--tw-content);
    width: 3rem;
  }

  .sm\:after\:w-12::after {
    content: var(--tw-content);
    width: 3rem;
  }
}@media (min-width: 768px) {

  .md\:static {
    position: static;
  }

  .md\:right-80 {
    right: 20rem;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:mt-14 {
    margin-top: 3.5rem;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-4 {
    width: 1rem;
  }

  .md\:min-w-0 {
    min-width: 0px;
  }

  .md\:max-w-\[544px\] {
    max-width: 544px;
  }

  .md\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:pt-10 {
    padding-top: 2.5rem;
  }

  .md\:text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}@media (min-width: 1024px) {

  .lg\:right-24 {
    right: 6rem;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:mt-28 {
    margin-top: 7rem;
  }

  .lg\:w-8 {
    width: 2rem;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-4 {
    gap: 1rem;
  }

  .lg\:p-12 {
    padding: 3rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .lg\:px-14 {
    padding-left: 3.5rem;
    padding-right: 3.5rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-14 {
    padding-top: 3.5rem;
  }

  .lg\:pt-8 {
    padding-top: 2rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@media (min-width: 1280px) {

  .xl\:right-52 {
    right: 13rem;
  }

  .xl\:max-w-full {
    max-width: 100%;
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:px-20 {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .xl\:pt-0 {
    padding-top: 0px;
  }

  .xl\:pt-16 {
    padding-top: 4rem;
  }

  .xl\:pt-8 {
    padding-top: 2rem;
  }
}.\[\&\:\:-webkit-scrollbar-thumb\]\:h-16::-webkit-scrollbar-thumb {
  height: 4rem;
}.\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-md::-webkit-scrollbar-thumb {
  border-radius: 0.375rem;
}.\[\&\:\:-webkit-scrollbar-track\]\:my-1::-webkit-scrollbar-track {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}.\[\&\:\:-webkit-scrollbar-track\]\:rounded-md::-webkit-scrollbar-track {
  border-radius: 0.375rem;
}.\[\&\:\:-webkit-scrollbar-track\]\:bg-white::-webkit-scrollbar-track {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}.\[\&\:\:-webkit-scrollbar\]\:h-1\/2::-webkit-scrollbar {
  height: 50%;
}.\[\&\:\:-webkit-scrollbar\]\:w-1\.5::-webkit-scrollbar {
  width: 0.375rem;
}.\[\&\:has\(\.max-w-container\)\]\:pb-5:has(.max-w-container) {
  padding-bottom: 1.25rem;
}@media (min-width: 768px) {

  .md\:\[\&\:has\(\.max-w-container\)\]\:pb-10:has(.max-w-container) {
    padding-bottom: 2.5rem;
  }
}@media (min-width: 1024px) {

  .lg\:\[\&\:has\(\.max-w-container\)\]\:pb-14:has(.max-w-container) {
    padding-bottom: 3.5rem;
  }
}@media (min-width: 1280px) {

  .xl\:\[\&\:has\(\.max-w-container\)\]\:pb-20:has(.max-w-container) {
    padding-bottom: 5rem;
  }
}.\[\&\>\:first-child\]\:pb-2\.5>:first-child {
  padding-bottom: 0.625rem;
}.\[\&\>\:last-child\]\:pt-2\.5>:last-child {
  padding-top: 0.625rem;
}.\[\&\>\:not\(\:first-child\2c \:last-child\)\]\:py-2\.5>:not(:first-child,:last-child) {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}.\[\&\>\:nth-child\(2\)\>\:nth-child\(2\)\>\:nth-child\(2\)\>div\]\:\!-mt-px>:nth-child(2)>:nth-child(2)>:nth-child(2)>div {
  margin-top: -1px !important;
}.\[\&\>div\]\:gap-6>div {
  gap: 1.5rem;
}.\[\&\>path\]\:stroke-\[2px\]>path {
  stroke-width: 2px;
}.\[\&_\.components\\-base\\-control\\_\\_field\]\:mb-0 .components\-base\-control__field {
  margin-bottom: 0px;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:mt-2 .components\-range\-control__thumb-wrapper {
  margin-top: 0.5rem;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:h-\[14px\] .components\-range\-control__thumb-wrapper {
  height: 14px;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:w-\[14px\] .components\-range\-control__thumb-wrapper {
  width: 14px;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:border .components\-range\-control__thumb-wrapper {
  border-width: 1px;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:border-solid .components\-range\-control__thumb-wrapper {
  border-style: solid;
}.\[\&_\.components\\-range\\-control\\_\\_thumb-wrapper\]\:border-white .components\-range\-control__thumb-wrapper {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
