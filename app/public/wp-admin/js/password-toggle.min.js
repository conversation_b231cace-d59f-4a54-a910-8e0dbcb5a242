/*! This file is auto-generated */
!function(){var t,e,s,i,a=wp.i18n.__;function d(){t=this.getAttribute("data-toggle"),e=this.parentElement.children.namedItem("pwd"),s=this.getElementsByClassName("dashicons")[0],i=this.getElementsByClassName("text")[0],0===parseInt(t,10)?(this.setAttribute("data-toggle",1),this.setAttribute("aria-label",a("Hide password")),e.setAttribute("type","text"),i.innerHTML=a("Hide"),s.classList.remove("dashicons-visibility"),s.classList.add("dashicons-hidden")):(this.setAttribute("data-toggle",0),this.setAttribute("aria-label",a("Show password")),e.setAttribute("type","password"),i.innerHTML=a("Show"),s.classList.remove("dashicons-hidden"),s.classList.add("dashicons-visibility"))}document.querySelectorAll(".pwd-toggle").forEach(function(t){t.classList.remove("hide-if-no-js"),t.addEventListener("click",d)})}();