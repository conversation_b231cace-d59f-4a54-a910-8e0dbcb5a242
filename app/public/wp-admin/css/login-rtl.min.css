/*! This file is auto-generated */
body,html{height:100%;margin:0;padding:0}body{background:#f0f0f1;min-width:0;color:#3c434a;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif;font-size:13px;line-height:1.4}a{color:#2271b1;transition-property:border,background,color;transition-duration:.05s;transition-timing-function:ease-in-out}a{outline:0}a:active,a:hover{color:#135e96}a:focus{color:#043959;box-shadow:0 0 0 2px #2271b1;outline:2px solid transparent}p{line-height:1.5}.login .message,.login .notice,.login .success{border-right:4px solid #72aee6;padding:12px;margin-right:0;margin-bottom:20px;background-color:#fff;box-shadow:0 1px 1px 0 rgba(0,0,0,.1);word-wrap:break-word}.login .success{border-right-color:#00a32a}.login .notice-error{border-right-color:#d63638}.login .login-error-list{list-style:none}.login .login-error-list li+li{margin-top:4px}#loginform p.submit,.login-action-lostpassword p.submit{border:none;margin:-10px 0 20px}.login *{margin:0;padding:0}.login .input::-ms-clear{display:none}.login .pw-weak{margin-bottom:15px}.login .button.wp-hide-pw{background:0 0;border:1px solid transparent;box-shadow:none;font-size:14px;line-height:2;width:2.5rem;height:2.5rem;min-width:40px;min-height:40px;margin:0;padding:5px 9px;position:absolute;left:0;top:0}.login .button.wp-hide-pw:hover{background:0 0}.login .button.wp-hide-pw:focus{background:0 0;border-color:#3582c4;box-shadow:0 0 0 1px #3582c4;outline:2px solid transparent}.login .button.wp-hide-pw:active{background:0 0;box-shadow:none;transform:none}.login .button.wp-hide-pw .dashicons{width:1.25rem;height:1.25rem;top:.25rem}.login .wp-pwd{position:relative}.no-js .hide-if-no-js{display:none}.login form{margin-top:20px;margin-right:0;padding:26px 24px;font-weight:400;overflow:hidden;background:#fff;border:1px solid #c3c4c7;box-shadow:0 1px 3px rgba(0,0,0,.04)}.login form.shake{animation:shake .2s cubic-bezier(.19,.49,.38,.79) both;animation-iteration-count:3;transform:translateX(0)}@keyframes shake{25%{transform:translateX(20px)}75%{transform:translateX(-20px)}100%{transform:translateX(0)}}@media (prefers-reduced-motion:reduce){.login form.shake{animation:none;transform:none}}.login-action-confirm_admin_email #login{width:60vw;max-width:650px;margin-top:-2vh}@media screen and (max-width:782px){.login-action-confirm_admin_email #login{box-sizing:border-box;margin-top:0;padding-right:4vw;padding-left:4vw;width:100vw}}.login form .forgetmenot{font-weight:400;float:right;margin-bottom:0}.login .button-primary{float:left}.login .reset-pass-submit{display:flex;flex-flow:row wrap;justify-content:space-between}.login .reset-pass-submit .button{display:inline-block;float:none;margin-bottom:6px}.login .admin-email-confirm-form .submit{text-align:center}.admin-email__later{text-align:right}.login form p.admin-email__details{margin:1.1em 0}.login .admin-email__heading{border-bottom:1px #f0f0f1 solid;color:#50575e;font-weight:400;padding-bottom:.5em;text-align:right}.admin-email__actions div{padding-top:1.5em}.login .admin-email__actions .button-primary{float:none;margin-right:.25em;margin-left:.25em}#login form p{margin-bottom:0}#login #reg_passmail,#login form .indicator-hint{margin-bottom:16px}#login form p.submit{margin:0;padding:0}.login label{font-size:14px;line-height:1.5;display:inline-block;margin-bottom:3px}.login .forgetmenot label,.login .pw-weak label{line-height:1.5;vertical-align:baseline}.login h1{text-align:center}.login h1 a{background-image:url(../images/w-logo-blue.png?ver=20131202);background-image:none,url(../images/wordpress-logo.svg?ver=20131107);background-size:84px;background-position:center top;background-repeat:no-repeat;color:#3c434a;height:84px;font-size:20px;font-weight:400;line-height:1.3;margin:0 auto 25px;padding:0;text-decoration:none;width:84px;text-indent:-9999px;outline:0;overflow:hidden;display:block}#login{width:320px;padding:5% 0 0;margin:auto}.login #backtoblog,.login #nav{font-size:13px;padding:0 24px}.login #nav{margin:24px 0 0}#backtoblog{margin:16px 0;word-wrap:break-word}.login #backtoblog a,.login #nav a{text-decoration:none;color:#50575e}.login #backtoblog a:hover,.login #nav a:hover,.login h1 a:hover{color:#135e96}.login #backtoblog a:focus,.login #nav a:focus,.login h1 a:focus{color:#043959}.login .privacy-policy-page-link{text-align:center;width:100%;margin:3em 0 2em}.login form .input,.login input[type=password],.login input[type=text]{font-size:24px;line-height:1.33333333;width:100%;border-width:.0625rem;padding:.1875rem .3125rem;margin:0 0 16px 6px;min-height:40px;max-height:none}.login input.password-input{font-family:Consolas,Monaco,monospace}.js.login input.password-input{padding-left:2.5rem}.login form .input,.login form input[type=checkbox],.login input[type=text]{background:#fff}.js.login-action-resetpass input[type=password],.js.login-action-resetpass input[type=text],.js.login-action-rp input[type=password],.js.login-action-rp input[type=text]{margin-bottom:0}.login #pass-strength-result{font-weight:600;margin:-1px 0 16px 5px;padding:6px 5px;text-align:center;width:100%}body.interim-login{height:auto}.interim-login #login{padding:0;margin:5px auto 20px}.interim-login.login h1 a{width:auto}.interim-login #login_error,.interim-login.login .message{margin:0 0 16px}.interim-login.login form{margin:0}.screen-reader-text,.screen-reader-text span{border:0;clip:rect(1px,1px,1px,1px);clip-path:inset(50%);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;word-wrap:normal!important}input::-ms-reveal{display:none}#language-switcher{padding:0;overflow:visible;background:0 0;border:none;box-shadow:none}#language-switcher select{margin-left:.25em}.language-switcher{margin:0 auto;padding:0 0 24px;text-align:center}.language-switcher label{margin-left:.25em}.language-switcher label .dashicons{width:auto;height:auto}.login .language-switcher .button{margin-bottom:0}@media screen and (max-height:550px){#login{padding:20px 0}#language-switcher{margin-top:0}}@media screen and (max-width:782px){.interim-login input[type=checkbox]{width:1rem;height:1rem}.interim-login input[type=checkbox]:checked:before{width:1.3125rem;height:1.3125rem;margin:-.1875rem -.25rem 0 0}#language-switcher label,#language-switcher select{margin-left:0}}@media screen and (max-width:400px){.login .language-switcher .button{display:block;margin:5px auto 0}}